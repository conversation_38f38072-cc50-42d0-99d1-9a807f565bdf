{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "prebuild:disabled": "node scripts/generate-static-data-api.js", "build": "next build", "build:static-data": "node scripts/generate-static-data-api.js", "build:static-data-direct": "node scripts/generate-static-data.js", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "type-check": "tsc --noEmit", "security:audit": "npm audit --audit-level=high", "security:scan": "trivy fs .", "docker:build": "./scripts/docker-build-optimized.sh", "docker:build:main": "./scripts/docker-build-optimized.sh main", "docker:build:logo": "./scripts/docker-build-optimized.sh logo", "docker:clean": "./scripts/docker-build-optimized.sh clean", "performance:lighthouse": "lhci autorun", "db:migrate": "supabase db push", "db:reset": "supabase db reset", "db:seed": "supabase db seed", "monitoring:dashboard": "open http://localhost:3000/api/monitoring/dashboard", "storage:health": "node scripts/check-storage-health.js", "storage:diagnostics": "node scripts/storage-diagnostics.js", "storage:status": "node scripts/simple-rate-limit-check.js", "env:verify": "npx dotenv-cli -e .env.local -- node scripts/verify-env-vars.js", "env:check": "node scripts/verify-env-vars.js"}, "dependencies": {"@emotion/is-prop-valid": "latest", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^3.9.1", "@lottiefiles/dotlottie-react": "latest", "@lottiefiles/dotlottie-web": "^0.48.0", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-aspect-ratio": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "latest", "@radix-ui/react-navigation-menu": "latest", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "latest", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "latest", "@reown/walletkit": "^1.2.8", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.50.3", "@types/bcrypt": "^5.0.2", "@types/pg": "^8.15.4", "@types/qrcode": "^1.5.5", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "@walletconnect/core": "^2.21.4", "@walletconnect/types": "^2.21.4", "@walletconnect/utils": "^2.21.4", "@walletconnect/web3wallet": "^1.16.0", "autoprefixer": "^10.4.20", "bcrypt": "^6.0.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "date-fns": "^4.1.0", "dotenv": "^17.0.0", "embla-carousel-react": "latest", "fast-text-encoding": "^1.0.6", "form-data": "^4.0.3", "framer-motion": "^12.23.0", "html5-qrcode": "^2.3.8", "input-otp": "latest", "jose": "^5.9.6", "lucide-react": "^0.454.0", "next-themes": "latest", "node-fetch": "^3.3.2", "pg": "^8.16.3", "postcss": "^8", "postgres": "^3.4.7", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "react": "^19", "react-day-picker": "latest", "react-dom": "^19", "react-hook-form": "latest", "react-native-get-random-values": "^1.11.0", "react-resizable-panels": "latest", "recharts": "latest", "sonner": "^2.0.5", "stripe": "^18.1.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "latest", "xrpl": "^4.3.0", "xrpl-txdata": "^2.0.1", "xumm": "^1.8.0", "xumm-oauth2-pkce": "^2.8.7", "xumm-sdk": "^1.11.2", "xumm-string-decode": "^0.7.1", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.27.0", "@lhci/cli": "^0.15.1", "@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.0.0", "@types/jest": "^29.5.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.26.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-tailwindcss": "^3.18.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jest-junit": "^16.0.0", "jest-watch-typeahead": "^2.2.2", "next": "^15.3.5", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}