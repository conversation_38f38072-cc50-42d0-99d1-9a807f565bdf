import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable standalone output for Docker optimization
  output: 'standalone',
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    remotePatterns: [
      new URL('https://hebbkx1anhila5yf.public.blob.vercel-storage.com/**'),
      new URL('https://haqbtbpmyadkocakqnew.supabase.co/**'),
      new URL('https://api.coingecko.com/**'),
      new URL('https://lottie.host/**'),
      new URL('https://xrplcluster.com/**'),
      new URL('https://s1.ripple.com/**'),
      new URL('https://s2.ripple.com/**'),
      new URL('https://firstledger.net/**'),
      new URL('https://xmagnetic.org/**'),
      new URL('https://dexscreener.com/**'),
      new URL('https://www.fuse.vip/**'),
      new URL('https://instagram.com/**'),
      new URL('https://youtube.com/**'),
      new URL('https://open.spotify.com/**'),
      // Docker logo processing service
      new URL('http://localhost:3001/**'),
      new URL('http://logo-processor:3001/**'),
    ],
    // Enhanced image optimization
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days for logos
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    unoptimized: false, // Enable optimization
  },
  // Simplified experimental features for Turbopack compatibility
  experimental: {
    // Only enable stable features
    scrollRestoration: true,
  },
  // Turbopack configuration (moved from experimental.turbo)
  // Note: enabled is not a valid option for turbopack config
  // turbopack: {
  //   // Disable turbopack for now to avoid conflicts
  //   enabled: false,
  // },
  // Performance optimizations
  poweredByHeader: false,
  generateEtags: true,
  onDemandEntries: {
    maxInactiveAge: 25 * 1000,
    pagesBufferLength: 2,
  },
  // External packages for server components
  serverExternalPackages: ['canvas-confetti', 'html5-qrcode', 'bcrypt', 'pg', 'postgres'],
  // Compress responses
  compress: true,
  // Static generation optimizations
  generateBuildId: async () => {
    // Use timestamp for build ID to ensure fresh static data
    return `static-${Date.now()}`
  },
  // Add security and permissions headers
  async headers() {
    return [
      {
        // Apply to all routes
        source: '/(.*)',
        headers: [
          {
            key: 'Permissions-Policy',
            value: 'payment=*, camera=*, microphone=*, geolocation=*, fullscreen=*'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com https://checkout.stripe.com https://xumm.app https://xaman.app https://oauth2.xumm.app https://oauth2.xaman.app https://unpkg.com https://va.vercel-scripts.com; connect-src 'self' https://haqbtbpmyadkocakqnew.supabase.co wss://haqbtbpmyadkocakqnew.supabase.co https://api.stripe.com https://checkout.stripe.com https://xrplcluster.com wss://xrplcluster.com https://s1.ripple.com https://s2.ripple.com https://api.coingecko.com https://firstledger.net https://xmagnetic.org https://dexscreener.com https://lottie.host https://hebbkx1anhila5yf.public.blob.vercel-storage.com http://localhost:3001 http://logo-processor:3001 https://xumm.app wss://xumm.app https://xaman.app wss://xaman.app https://oauth2.xumm.app https://oauth2.xaman.app https://relay.walletconnect.com wss://relay.walletconnect.com https://relay.walletconnect.org wss://relay.walletconnect.org https://va.vercel-scripts.com; frame-src 'self' https://js.stripe.com https://hooks.stripe.com https://checkout.stripe.com https://xumm.app https://xaman.app https://oauth2.xumm.app https://oauth2.xaman.app https://lottie.host; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: https:; font-src 'self' data:; media-src 'self' https://lottie.host data: blob:;"
          }
        ]
      }
    ]
  },
  async redirects() {
    return [
      {
        source: '/card',
        destination: '/',
        permanent: true
      },
      {
        source: '/network',
        destination: '/',
        permanent: true
      },
      {
        source: '/solutions',
        destination: '/fuse',
        permanent: true
      },
      {
        source: '/update-password',
        destination: '/account/update-password',
        permanent: false
      }
    ];
  },
  // Webpack config for preventing client-side bundling of Node.js modules
  webpack: (config, { isServer, nextRuntime }) => {
    if (!isServer) {
      // Exclude Node.js modules from client-side bundle
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
        dns: false,
        pg: false,
        'pg-connection-string': false,
        'pg-pool': false,
        'pg-cursor': false,
        'pg-native': false,
        'pg-query-stream': false,
        'pg-types': false,
        'pg-int8': false,
        'postgres': false,
      };
    }

    // Handle problematic modules for different runtimes
    if (nextRuntime === 'edge') {
      config.externals = config.externals || [];
      config.externals.push('@supabase/ssr', '@supabase/supabase-js');
    }

    // Optimize webpack caching for large strings
    if (config.cache && config.cache.type === 'filesystem') {
      config.cache.compression = 'gzip';
      config.cache.maxMemoryGenerations = 1;

      // Configure cache to handle large strings better
      config.cache.cacheDirectory = path.join(__dirname, '.next/cache/webpack');
      config.cache.buildDependencies = {
        config: [__filename],
      };

      // Optimize serialization for large data
      config.cache.store = 'pack';
      config.cache.version = '1.0.0';

      // Handle large string serialization warning
      config.cache.maxAge = 5184000000; // 60 days
      config.cache.profile = false;
    }

    // Suppress large string serialization warnings
    config.infrastructureLogging = {
      level: 'error',
      debug: false
    };

    // Optimize performance for large codebases
    config.optimization = {
      ...config.optimization,
      moduleIds: 'deterministic',
      chunkIds: 'deterministic',
      splitChunks: {
        ...config.optimization.splitChunks,
        cacheGroups: {
          ...config.optimization.splitChunks?.cacheGroups,
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            maxSize: 244000, // 244KB to avoid large string warnings
          },
        },
      },
    };

    // Optimize module resolution
    config.resolve.modules = ['node_modules', path.join(__dirname, 'node_modules')];

    return config;
  },
}

export default nextConfig
