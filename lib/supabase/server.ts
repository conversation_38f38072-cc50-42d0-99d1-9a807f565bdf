import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { createClient as createSupabaseClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'

export const createClient = async () => {
  // Check if environment variables are defined
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    throw new Error('Supabase environment variables are missing')
  }

  // Dynamic import of next/headers to avoid client-side issues
  let cookieStore = null;
  
  if (typeof window === 'undefined') {
    try {
      const { cookies } = await import("next/headers");
      cookieStore = await cookies();
    } catch (error) {
      console.warn('Failed to import next/headers:', error);
    }
  }

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        getAll() {
          return cookieStore?.getAll() || []
        },
        setAll(cookiesToSet) {
          try {
            if (cookieStore) {
              cookiesToSet.forEach(({ name, value, options }) => {
                cookieStore.set(name, value, options || {})
              })
            }
          } catch (error) {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
            console.warn('Cookie setting failed:', error)
          }
        },
      },
    },
  )
}

/**
 * Create Supabase admin client with service role key
 * Use this for server-side operations that don't require user authentication
 */
export const createAdminClient = () => {
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
    throw new Error('Supabase environment variables are missing')
  }

  return createSupabaseClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
}



/**
 * Create Supabase client for middleware
 */
export const createMiddlewareClient = (request: NextRequest, response: NextResponse) => {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            response.cookies.set(name, value, options)
          )
        },
      },
    },
  )
}

/**
 * Enhanced error handling for Supabase operations
 */
function handleSupabaseError(error: any, operation: string): void {
  // Properly serialize error for logging
  const errorDetails = {
    message: error?.message || 'Unknown error',
    code: error?.code || 'NO_CODE',
    details: error?.details || 'No details',
    hint: error?.hint || 'No hint',
    status: error?.status || 'No status',
    statusText: error?.statusText || 'No status text',
    full_error: error
  };

  console.error(`Supabase ${operation} error:`, JSON.stringify(errorDetails, null, 2));

  if (error?.code === 'PGRST202') {
    console.error(`PGRST202 in ${operation}: No rows returned. Check RLS policies and user authentication.`);
  }

  if (error?.code === 'PGRST116') {
    console.error(`PGRST116 in ${operation}: No rows found. This might be expected for new users.`);
  }

  if (error?.message?.includes('permission denied')) {
    console.error(`Permission denied in ${operation}: Check RLS policies for authenticated users.`);
  }

  if (error?.message?.includes('JWT')) {
    console.error(`JWT error in ${operation}: User session may be invalid or expired.`);
  }

  if (error?.message?.includes('relation') && error?.message?.includes('does not exist')) {
    console.error(`Table/relation error in ${operation}: Database table may not exist or be accessible.`);
  }
}

/**
 * Get authenticated user from Supabase session with enhanced error handling
 */
export async function getAuthenticatedUser(): Promise<any | null> {
  console.log('🔐 Getting authenticated user from Supabase session...')

  try {
    const supabase = await createClient()
    const { data: { user }, error } = await supabase.auth.getUser()

    if (error) {
      handleSupabaseError(error, 'getUser');
      return null
    }

    if (user) {
      console.log('✅ Successfully authenticated user:', user.id)
      return {
        sub: user.id,
        email: user.email,
        role: user.role
      }
    } else {
      console.log('⚠️ No authenticated user found')
      return null
    }
  } catch (error) {
    handleSupabaseError(error, 'getAuthenticatedUser');
    return null
  }
}

/**
 * Get user session with enhanced error handling
 */
export async function getUserSession() {
  try {
    console.log('🔑 Getting user session...');
    const supabase = await createClient()
    const { data: { session }, error } = await supabase.auth.getSession()

    if (error) {
      handleSupabaseError(error, 'getSession');
      return { session: null, error }
    }

    if (session) {
      console.log('✅ Valid session found for user:', session.user.id);
    } else {
      console.log('⚠️ No active session found');
    }

    return { session, error: null }
  } catch (error) {
    handleSupabaseError(error, 'getUserSession');
    return { session: null, error }
  }
}

/**
 * Get user profile using direct database access (bypasses PostgREST)
 */
export async function getUserProfile(userId: string) {
  try {
    if (!userId) {
      console.error('getUserProfile: No userId provided');
      return null;
    }

    console.log(`👤 Fetching profile using DIRECT DATABASE ACCESS for user: ${userId}`);

    // Import and use direct database access
    const { getUserProfileDirect } = await import('@/lib/database-direct');
    const profile = await getUserProfileDirect(userId);

    if (profile) {
      console.log(`✅ Profile found via DIRECT DATABASE for user: ${userId}`, {
        id: profile.id,
        email: profile.user_email,
        firstName: profile.first_name,
        lastName: profile.last_name
      });
    } else {
      console.log(`ℹ️ No profile found via DIRECT DATABASE for user: ${userId}`);
    }

    return profile;
  } catch (error) {
    console.error('getUserProfile: Direct database access failed:', error);
    return null;
  }
}

/**
 * Test database connection and RLS policies
 */
export async function testDatabaseAccess(userId: string) {
  console.log(`🧪 Testing database access for user: ${userId}`);

  const supabase = await createClient();
  const results = {
    profile: false,
    businesses: false,
    purchases: false,
    physical_cards: false,
    qr_interactions: false,
    referrals: false,
    business_visits: false
  };

  // Test each table access
  try {
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .single();

    results.profile = !profileError;
    if (profileError) handleSupabaseError(profileError, 'profile test');
  } catch (error) {
    handleSupabaseError(error, 'profile test');
  }

  try {
    const { data: businesses, error: businessError } = await supabase
      .from('businesses')
      .select('id')
      .eq('user_id', userId)
      .limit(1);

    results.businesses = !businessError;
    if (businessError) handleSupabaseError(businessError, 'businesses test');
  } catch (error) {
    handleSupabaseError(error, 'businesses test');
  }

  try {
    const { data: purchases, error: purchaseError } = await supabase
      .from('purchases')
      .select('id')
      .eq('user_id', userId)
      .limit(1);

    results.purchases = !purchaseError;
    if (purchaseError) handleSupabaseError(purchaseError, 'purchases test');
  } catch (error) {
    handleSupabaseError(error, 'purchases test');
  }

  try {
    const { data: cards, error: cardError } = await supabase
      .from('physical_cards')
      .select('id')
      .eq('user_id', userId)
      .limit(1);

    results.physical_cards = !cardError;
    if (cardError) handleSupabaseError(cardError, 'physical_cards test');
  } catch (error) {
    handleSupabaseError(error, 'physical_cards test');
  }

  try {
    const { data: qr, error: qrError } = await supabase
      .from('qr_interactions')
      .select('id')
      .or(`scanner_user_id.eq.${userId},scanned_user_id.eq.${userId}`)
      .limit(1);

    results.qr_interactions = !qrError;
    if (qrError) handleSupabaseError(qrError, 'qr_interactions test');
  } catch (error) {
    handleSupabaseError(error, 'qr_interactions test');
  }

  try {
    const { data: referrals, error: referralError } = await supabase
      .from('referrals')
      .select('id')
      .eq('user_id', userId)
      .limit(1);

    results.referrals = !referralError;
    if (referralError) handleSupabaseError(referralError, 'referrals test');
  } catch (error) {
    handleSupabaseError(error, 'referrals test');
  }

  try {
    const { data: visits, error: visitError } = await supabase
      .from('business_visits')
      .select('id')
      .eq('user_id', userId)
      .limit(1);

    results.business_visits = !visitError;
    if (visitError) handleSupabaseError(visitError, 'business_visits test');
  } catch (error) {
    handleSupabaseError(error, 'business_visits test');
  }

  console.log('🧪 Database access test results:', results);
  return results;
}
