"use client"

import { useEffect, useRef } from "react"

export function MissionHeadlineAnimation() {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Create a script element
    const script = document.createElement("script")
    script.src = "https://unpkg.com/@dotlottie/player-component@2.7.12/dist/dotlottie-player.mjs"
    script.type = "module"

    // Add the script to the document
    document.head.appendChild(script)

    // Wait for the script to load
    script.onload = () => {
      // Check if the custom element is defined
      if (customElements.get("dotlottie-player") && containerRef.current) {
        // Clear any existing content
        containerRef.current.innerHTML = `
          <dotlottie-player 
            src="https://lottie.host/9b3b5a23-79cb-449b-a523-7cab48ff6aa2/SnOFt96zlp.lottie" 
            background="transparent" 
            speed="1" 
            style="width: 60px; height: 60px;" 
            loop 
            autoplay>
          </dotlottie-player>
        `
      }
    }

    return () => {
      // Clean up
      if (containerRef.current) {
        containerRef.current.innerHTML = ""
      }
    }
  }, [])

  return (
    <div ref={containerRef} className="inline-block align-middle">
      {/* Lottie animation will be inserted here */}
    </div>
  )
}
