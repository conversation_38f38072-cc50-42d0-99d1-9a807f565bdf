"use client"

import { useEffect, useState } from "react"
import dynamic from "next/dynamic"

// Dynamically import the DotLottieReact component to avoid SSR issues
const DotLottieReact = dynamic(() => import("@lottiefiles/dotlottie-react").then((mod) => mod.DotLottieReact), {
  ssr: false,
})

export function BubbleAnimation() {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  if (!isClient) {
    return <div className="w-16 h-16 rounded-full bg-gray-200 animate-pulse"></div>
  }

  return (
    <div className="w-16 h-16">
      <DotLottieReact src="https://lottie.host/4d1f0743-dbdc-44b6-a8ce-2ccd0741c428/upqyw8Z5Y3.lottie" loop autoplay />
    </div>
  )
}
