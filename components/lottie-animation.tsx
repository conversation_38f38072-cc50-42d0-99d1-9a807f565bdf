"use client"

import { DotLottieWCAnimation } from "./dotlottie-wc-animation"

interface LottieAnimationProps {
  src?: string
  width?: string
  height?: string
  className?: string
}

export function LottieAnimation({
  src = "https://lottie.host/2de3886b-81e9-40c9-a364-dce493cfc31a/APDopJg77F.lottie",
  width = "300px",
  height = "300px",
  className = "",
}: LottieAnimationProps) {
  return (
    <div className={`flex justify-center items-center py-4 ${className}`}>
      <DotLottieWCAnimation
        src={src}
        width={width}
        height={height}
        speed="1"
        autoplay={true}
        loop={true}
      />
    </div>
  )
}
