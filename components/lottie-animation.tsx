"use client"

import { useEffect, useRef } from "react"
import <PERSON>ript from "next/script"

interface LottieAnimationProps {
  src?: string
  width?: string
  height?: string
  className?: string
}

export function LottieAnimation({
  src = "https://lottie.host/2de3886b-81e9-40c9-a364-dce493cfc31a/APDopJg77F.lottie",
  width = "300px",
  height = "300px",
  className = "",
}: LottieAnimationProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const loaded = useRef(false)

  useEffect(() => {
    if (loaded.current || !containerRef.current) return

    // Create the dotlottie-player element
    const player = document.createElement("dotlottie-player")

    // Use the original URL without modification
    player.setAttribute("src", src)
    player.setAttribute("background", "transparent")
    player.setAttribute("speed", "1")
    player.setAttribute("loop", "")
    player.setAttribute("autoplay", "")
    player.style.width = width
    player.style.height = height
    player.style.margin = "0 auto"

    // Add error handling
    player.addEventListener("error", (event) => {
      console.error("Lottie player error:", event)

      // Create a fallback element to show when animation fails to load
      const fallback = document.createElement("div")
      fallback.textContent = "Animation"
      fallback.style.width = width
      fallback.style.height = height
      fallback.style.display = "flex"
      fallback.style.alignItems = "center"
      fallback.style.justifyContent = "center"
      fallback.style.backgroundColor = "#f0f0f0"
      fallback.style.borderRadius = "8px"
      fallback.style.color = "#666"
      fallback.style.fontWeight = "500"

      // Replace the player with the fallback
      if (containerRef.current && containerRef.current.contains(player)) {
        containerRef.current.replaceChild(fallback, player)
      }
    })

    // Append the player to the container
    containerRef.current.appendChild(player)
    loaded.current = true

    return () => {
      if (containerRef.current && containerRef.current.contains(player)) {
        containerRef.current.removeChild(player)
      }
    }
  }, [src, width, height])

  return (
    <>
      <Script
        src="https://unpkg.com/@dotlottie/player-component@latest/dist/dotlottie-player.mjs"
        type="module"
        strategy="afterInteractive"
      />
      <div ref={containerRef} className={`flex justify-center items-center py-4 ${className}`}></div>
    </>
  )
}
