"use client"

import { useEffect, useRef } from "react"

interface DotLottieWCAnimationProps {
  src: string
  width?: string
  height?: string
  speed?: string
  autoplay?: boolean
  loop?: boolean
  className?: string
  style?: React.CSSProperties
}

export function DotLottieWCAnimation({
  src,
  width = "300px",
  height = "300px", 
  speed = "1",
  autoplay = true,
  loop = true,
  className = "",
  style = {}
}: DotLottieWCAnimationProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const scriptLoadedRef = useRef(false)

  useEffect(() => {
    const loadScript = async () => {
      // Check if script is already loaded
      if (scriptLoadedRef.current || document.querySelector('script[src*="dotlottie-wc"]')) {
        createAnimation()
        return
      }

      try {
        // Create and load the script
        const script = document.createElement("script")
        script.src = "https://unpkg.com/@lottiefiles/dotlottie-wc@0.6.2/dist/dotlottie-wc.js"
        script.type = "module"
        
        script.onload = () => {
          scriptLoadedRef.current = true
          createAnimation()
        }
        
        script.onerror = (error) => {
          console.error("Failed to load dotlottie-wc script:", error)
          createFallback()
        }

        document.head.appendChild(script)
      } catch (error) {
        console.error("Error loading dotlottie-wc:", error)
        createFallback()
      }
    }

    const createAnimation = () => {
      if (!containerRef.current) return

      // Clear any existing content
      containerRef.current.innerHTML = ""

      try {
        // Create the dotlottie-wc element
        const lottieElement = document.createElement("dotlottie-wc")
        lottieElement.setAttribute("src", src)
        lottieElement.setAttribute("speed", speed)
        
        // Set style attributes
        lottieElement.style.width = width
        lottieElement.style.height = height
        
        // Apply additional styles
        Object.assign(lottieElement.style, style)
        
        // Set boolean attributes
        if (autoplay) {
          lottieElement.setAttribute("autoplay", "")
        }
        if (loop) {
          lottieElement.setAttribute("loop", "")
        }

        // Add error handling
        lottieElement.addEventListener("error", (event) => {
          console.error("Lottie animation error:", event)
          createFallback()
        })

        // Add load event listener
        lottieElement.addEventListener("load", () => {
          console.log("Lottie animation loaded successfully")
        })

        containerRef.current.appendChild(lottieElement)
      } catch (error) {
        console.error("Error creating dotlottie-wc element:", error)
        createFallback()
      }
    }

    const createFallback = () => {
      if (!containerRef.current) return

      containerRef.current.innerHTML = `
        <div style="
          width: ${width}; 
          height: ${height}; 
          display: flex; 
          align-items: center; 
          justify-content: center; 
          background-color: #f0f0f0; 
          border-radius: 8px; 
          color: #666; 
          font-weight: 500;
          font-size: 14px;
        ">
          Animation Loading...
        </div>
      `
    }

    loadScript()

    return () => {
      // Cleanup if needed
      if (containerRef.current) {
        containerRef.current.innerHTML = ""
      }
    }
  }, [src, width, height, speed, autoplay, loop, style])

  return (
    <div 
      ref={containerRef} 
      className={`inline-block ${className}`}
      style={{ width, height }}
    >
      {/* Lottie animation will be inserted here */}
    </div>
  )
}
