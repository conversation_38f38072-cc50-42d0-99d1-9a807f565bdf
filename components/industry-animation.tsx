"use client"

import { useEffect, useRef } from "react"

export function IndustryAnimation() {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Create script element for dotlottie-player
    const script = document.createElement("script")
    script.src = "https://unpkg.com/@dotlottie/player-component@2.7.12/dist/dotlottie-player.mjs"
    script.type = "module"
    document.head.appendChild(script)

    // Create the dotlottie-player element
    const createPlayer = () => {
      if (containerRef.current) {
        // Clear any existing content
        containerRef.current.innerHTML = ""

        // Create the player element
        const player = document.createElement("dotlottie-player")
        player.setAttribute("src", "https://lottie.host/bff023a8-ddc5-4788-adf5-e8ef59e1e088/e749q9gbhS.lottie")
        player.setAttribute("background", "transparent")
        player.setAttribute("speed", "1")
        player.setAttribute("loop", "")
        player.setAttribute("autoplay", "")
        player.style.width = "300px"
        player.style.height = "300px"

        // Append the player to the container
        containerRef.current.appendChild(player)
      }
    }

    // Wait for script to load before creating player
    script.onload = createPlayer

    // If script is already loaded, create player immediately
    if (
      document.querySelector(
        'script[src="https://unpkg.com/@dotlottie/player-component@2.7.12/dist/dotlottie-player.mjs"]',
      )
    ) {
      createPlayer()
    }

    // Cleanup function
    return () => {
      // Only remove the script if we added it
      const existingScript = document.querySelector(
        'script[src="https://unpkg.com/@dotlottie/player-component@2.7.12/dist/dotlottie-player.mjs"]',
      )
      if (existingScript && script === existingScript) {
        document.head.removeChild(script)
      }
    }
  }, [])

  return <div ref={containerRef} className="flex justify-center items-center"></div>
}
