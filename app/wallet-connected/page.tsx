"use client"

import { useEffect, useState, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { motion } from "framer-motion"
import { CheckCircle, Loader2, AlertCircle } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { getSupabaseClient } from "@/lib/supabase"
import { profileCache } from "@/lib/profile-cache"
import { authStateManager } from "@/lib/auth-state-manager"
import { walletService } from "@/lib/wallet-service"

function WalletConnectedContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { user, profile } = useAuth()
  const [isProcessing, setIsProcessing] = useState(false)
  const [processingStatus, setProcessingStatus] = useState<'checking' | 'processing' | 'success' | 'error' | 'auth_required'>('checking')
  const [errorMessage, setErrorMessage] = useState<string>('')

  useEffect(() => {
    const handleOAuthCallback = async () => {
      // Check for wallet return first
      const walletReturn = authStateManager.isWalletReturn()
      
      if (walletReturn.isReturn && walletReturn.sessionId) {
        console.log('🔄 [WalletConnected] Wallet return detected:', walletReturn)
        setIsProcessing(true)
        setProcessingStatus('processing')

        try {
          // Try to restore authentication state
          const restoration = authStateManager.restoreAuthState(walletReturn.sessionId)
          
          if (restoration.success && restoration.state) {
            console.log('✅ [WalletConnected] Auth state restored successfully:', {
              userId: restoration.state.userId,
              targetRoute: restoration.state.targetRoute,
              walletContext: restoration.state.walletContext
            })

            // Check if user is currently authenticated
            if (!user?.id) {
              console.log('🔑 [WalletConnected] User not authenticated, redirecting to login for restoration')
              const loginUrl = `/login?wallet_return=true&session_id=${walletReturn.sessionId}&redirect=${encodeURIComponent(restoration.state.targetRoute)}`
              router.push(loginUrl)
              return
            }

            // User is authenticated, mark as success and redirect to target route
            console.log('✅ [WalletConnected] User authenticated, processing wallet return')
            setProcessingStatus('success')
            
            // Clean up auth state and redirect after a short delay
            setTimeout(() => {
              if (walletReturn.sessionId) {
                authStateManager.cleanupAuthState(walletReturn.sessionId)
              }
              if (restoration.state?.targetRoute) {
                router.push(restoration.state.targetRoute)
              }
            }, 1500)
            return
          } else {
            console.warn('⚠️ [WalletConnected] Failed to restore auth state:', restoration.error)
          }
        } catch (restoreError) {
          console.error('❌ [WalletConnected] Error during wallet return processing:', restoreError)
        }
      }

      // Standard OAuth callback handling
      const authCode = searchParams.get('code')
      const state = searchParams.get('state')
      const error = searchParams.get('error')

      if (authCode || error) {
        console.log('🔐 OAuth callback detected on wallet-connected:', { authCode: !!authCode, state, error })
        setIsProcessing(true)
        setProcessingStatus('processing')

        try {
          if (error) {
            console.error('❌ OAuth error:', error)
            setProcessingStatus('error')
            setErrorMessage(`OAuth error: ${error}`)
            return
          }

          if (authCode) {
            console.log('🔄 Processing OAuth authorization code on wallet-connected...')

            // Exchange authorization code for access token via our API
            const { authenticatedFetch } = await import('@/lib/auth-client');
            const tokenResponse = await authenticatedFetch('/api/xaman/oauth/token', {
              method: 'POST',
              body: JSON.stringify({
                code: authCode,
                state: state,
                redirect_uri: window.location.origin + '/wallet-connected'
              }),
            })

            if (!tokenResponse.ok) {
              const errorData = await tokenResponse.json()
              throw new Error(`Token exchange failed: ${errorData.error}`)
            }

            const tokenData = await tokenResponse.json()
            console.log('✅ OAuth token exchange successful:', {
              hasAccessToken: !!tokenData.access_token,
              walletAddress: tokenData.user?.account
            })

            // If user is authenticated, update profile immediately
            if (user?.id && tokenData.user?.account) {
              console.log('🔄 [WalletConnected] User authenticated, updating wallet profile...')
              try {
                await updateUserWalletProfile(user.id, tokenData.user.account)
                setProcessingStatus('success')
              } catch (updateError) {
                console.error('❌ [WalletConnected] Failed to update wallet profile:', updateError)
                setProcessingStatus('error')
                setErrorMessage(updateError instanceof Error ? updateError.message : 'Failed to save wallet address')
                return
              }
            } else if (tokenData.user?.account) {
              // Try to find user by stored authentication context
              console.log('👤 [WalletConnected] User not in current context - checking for stored auth...')
              
              // Check if we have a stored user context from the wallet flow initiation
              const storedAuthContext = localStorage.getItem('wallet_flow_user_context')
              let userIdToUpdate: string | null = null
              
              if (storedAuthContext) {
                try {
                  const authContext = JSON.parse(storedAuthContext)
                  if (authContext.userId && authContext.timestamp) {
                    // Verify the stored context is recent (within 10 minutes)
                    const contextAge = Date.now() - new Date(authContext.timestamp).getTime()
                    if (contextAge < 10 * 60 * 1000) { // 10 minutes
                      userIdToUpdate = authContext.userId
                      console.log('✅ [WalletConnected] Found valid stored auth context for user:', userIdToUpdate)
                    } else {
                      console.warn('⚠️ [WalletConnected] Stored auth context expired')
                      localStorage.removeItem('wallet_flow_user_context')
                    }
                  }
                } catch (error) {
                  console.error('❌ [WalletConnected] Error parsing stored auth context:', error)
                  localStorage.removeItem('wallet_flow_user_context')
                }
              }

              if (userIdToUpdate) {
                // We have a valid user ID from stored context - update profile directly
                console.log('🔄 [WalletConnected] Updating wallet profile using stored user context...')
                try {
                  await updateUserWalletProfile(userIdToUpdate, tokenData.user.account)
                  setProcessingStatus('success')
                  
                  // Clean up stored context
                  localStorage.removeItem('wallet_flow_user_context')
                } catch (updateError) {
                  console.error('❌ [WalletConnected] Failed to update wallet profile with stored context:', updateError)
                  setProcessingStatus('error')
                  setErrorMessage(updateError instanceof Error ? updateError.message : 'Failed to save wallet address')
                  return
                }
              } else {
                // Store wallet data temporarily for when user authenticates
                console.log('👤 [WalletConnected] No valid user context - storing wallet data temporarily')
                localStorage.setItem('pending_wallet_address', tokenData.user.account)
                localStorage.setItem('pending_wallet_timestamp', new Date().toISOString())
                setProcessingStatus('auth_required')
                return
              }
            } else {
              console.warn('⚠️ [WalletConnected] Cannot update wallet profile:', {
                hasUser: !!user?.id,
                hasWalletAddress: !!tokenData.user?.account,
                userId: user?.id
              })
              setProcessingStatus('error')
              setErrorMessage('No wallet address received from Xaman')
              return
            }

            // Store wallet connection state
            localStorage.setItem('fuse_wallet_address', tokenData.user.account)
            localStorage.setItem('fuse_wallet_connected', 'true')

            // Clean up URL parameters after processing
            const cleanUrl = window.location.pathname
            window.history.replaceState({}, document.title, cleanUrl)

            console.log('🎉 OAuth callback processed successfully on wallet-connected, URL cleaned')
          }
        } catch (callbackError) {
          console.error('❌ Error processing OAuth callback:', callbackError)
          setProcessingStatus('error')
          setErrorMessage(callbackError instanceof Error ? callbackError.message : 'Unknown error')
        } finally {
          setIsProcessing(false)
        }
      } else {
        // No OAuth parameters, just show success and redirect
        setProcessingStatus('success')
      }
    }

    // Helper function to update user profile with wallet address using RLS-bypassing API
    const updateUserWalletProfile = async (userId: string, walletAddress: string) => {
      if (!userId) {
        console.warn('⚠️ [WalletConnected] Cannot update wallet profile: User ID not provided')
        throw new Error('User ID is required')
      }

      if (!walletAddress) {
        console.warn('⚠️ [WalletConnected] Cannot update wallet profile: Wallet address not provided')
        throw new Error('Wallet address is required')
      }

      console.log('💾 [WalletConnected] Updating wallet profile via RLS-bypassing API:', {
        userId,
        walletAddress: `${walletAddress.substring(0, 6)}...${walletAddress.substring(walletAddress.length - 4)}`
      })

      try {
        // Use direct API call to bypass RLS issues in unauthenticated context
        const response = await fetch('/api/wallet-connection', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId,
            walletAddress,
            walletType: 'xrp',
            action: 'connect'
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('❌ [WalletConnected] API request failed:', errorData);
          throw new Error(errorData.error || `HTTP ${response.status}: Failed to update wallet profile`);
        }

        const result = await response.json();
        
        if (!result.success) {
          console.error('❌ [WalletConnected] API returned error:', result.error);
          throw new Error(result.error || 'Failed to update wallet profile');
        }

        console.log('✅ [WalletConnected] Wallet profile updated successfully via RLS-bypassing API')
      } catch (error) {
        console.error('❌ [WalletConnected] Exception updating user wallet profile:', error)
        throw error
      }
    }

    handleOAuthCallback()
  }, [searchParams, user])

  // Auto-redirect after successful processing
  useEffect(() => {
    if (processingStatus === 'success') {
      const timer = setTimeout(() => {
        router.push("/dashboard")
      }, 2000)
      return () => clearTimeout(timer)
    } else if (processingStatus === 'auth_required') {
      const timer = setTimeout(() => {
        // Redirect to login with return path
        router.push(`/login?redirect=${encodeURIComponent('/dashboard')}&wallet_pending=true`)
      }, 3000)
      return () => clearTimeout(timer)
    }
  }, [processingStatus, router])

  const renderContent = () => {
    switch (processingStatus) {
      case 'checking':
      case 'processing':
        return (
          <>
            <motion.div
              className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-6"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, duration: 0.5, type: "spring" }}
            >
              <Loader2 className="w-8 h-8 text-blue-400 animate-spin" />
            </motion.div>

            <motion.h1
              className="text-2xl font-bold text-white mb-4"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              {processingStatus === 'checking' ? 'Checking Wallet Connection...' : 'Connecting Wallet...'}
            </motion.h1>

            <motion.p
              className="text-gray-300 mb-6"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              Processing your XAMAN wallet connection. Please wait...
            </motion.p>

            <motion.div
              className="flex items-center justify-center space-x-2 text-blue-400"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
            >
              <Loader2 className="w-4 h-4 animate-spin" />
              <span className="text-sm">Processing...</span>
            </motion.div>
          </>
        )

      case 'success':
        return (
          <>
            <motion.div
              className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, duration: 0.5, type: "spring" }}
            >
              <CheckCircle className="w-8 h-8 text-green-400" />
            </motion.div>

            <motion.h1
              className="text-2xl font-bold text-white mb-4"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              Wallet Connected!
            </motion.h1>

            <motion.p
              className="text-gray-300 mb-6"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              Your XAMAN wallet has been successfully connected to $Fuse Rewards. 
              You can now start earning FUSE rewards!
            </motion.p>

            <motion.div
              className="flex items-center justify-center space-x-2 text-blue-400"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
            >
              <Loader2 className="w-4 h-4 animate-spin" />
              <span className="text-sm">Redirecting to dashboard...</span>
            </motion.div>
          </>
        )

      case 'auth_required':
        return (
          <>
            <motion.div
              className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-6"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, duration: 0.5, type: "spring" }}
            >
              <AlertCircle className="w-8 h-8 text-yellow-400" />
            </motion.div>

            <motion.h1
              className="text-2xl font-bold text-white mb-4"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              Authentication Required
            </motion.h1>

            <motion.p
              className="text-gray-300 mb-6"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              Your wallet has been verified! Please sign in or create an account to complete the connection.
            </motion.p>

            <motion.div
              className="flex items-center justify-center space-x-2 text-yellow-400"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
            >
              <Loader2 className="w-4 h-4 animate-spin" />
              <span className="text-sm">Redirecting to sign in...</span>
            </motion.div>
          </>
        )

      case 'error':
        return (
          <>
            <motion.div
              className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, duration: 0.5, type: "spring" }}
            >
              <AlertCircle className="w-8 h-8 text-red-400" />
            </motion.div>

            <motion.h1
              className="text-2xl font-bold text-white mb-4"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              Connection Failed
            </motion.h1>

            <motion.p
              className="text-gray-300 mb-6"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              {errorMessage || 'There was an error connecting your wallet. Please try again.'}
            </motion.p>

            <motion.div
              className="flex items-center justify-center space-x-2 text-red-400"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
            >
              <button 
                onClick={() => router.push('/dashboard')}
                className="bg-red-500/20 hover:bg-red-500/30 border border-red-400/30 text-red-300 hover:text-red-200 px-4 py-2 rounded-lg"
              >
                Return to Dashboard
              </button>
            </motion.div>
          </>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black flex items-center justify-center p-4">
      <motion.div
        className="bg-gradient-to-br from-gray-900/80 to-black/80 backdrop-blur-sm rounded-2xl border border-gray-700 p-8 text-center max-w-md w-full"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        {renderContent()}
      </motion.div>
    </div>
  )
}

export default function WalletConnectedPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black flex items-center justify-center p-4">
        <motion.div
          className="bg-gradient-to-br from-gray-900/80 to-black/80 backdrop-blur-sm rounded-2xl border border-gray-700 p-8 text-center max-w-md w-full"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <Loader2 className="w-8 h-8 text-blue-400 animate-spin" />
          </div>
          <h1 className="text-2xl font-bold text-white mb-4">
            Loading...
          </h1>
          <p className="text-gray-300 mb-6">
            Preparing wallet connection page...
          </p>
        </motion.div>
      </div>
    }>
      <WalletConnectedContent />
    </Suspense>
  )
}