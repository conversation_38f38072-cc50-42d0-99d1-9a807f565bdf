import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@12.5.0?target=deno";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.0.0";

// Initialize Stripe with proper error handling
const stripeSecretKey = Deno.env.get('STRIPE_SECRET_KEY');
if (!stripeSecretKey) {
  throw new Error("STRIPE_SECRET_KEY environment variable is not set");
}

const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2022-11-15',
  httpClient: Stripe.createFetchHttpClient()
});

const endpointSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET');
if (!endpointSecret) {
  throw new Error("STRIPE_WEBHOOK_SECRET environment variable is not set");
}
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': '*'
};
serve(async (req)=>{
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }
  try {
    const body = await req.text();
    const sig = req.headers.get('stripe-signature');
    if (!sig || !endpointSecret) {
      throw new Error('Missing signature or endpoint secret');
    }
    let event;
    try {
      event = await stripe.webhooks.constructEventAsync(body, sig, endpointSecret, undefined, Stripe.createSubtleCryptoProvider());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('⚠️ Webhook signature verification failed:', errorMessage);
      return new Response(JSON.stringify({
        error: 'Webhook signature verification failed'
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    const supabaseAdmin = createClient(Deno.env.get('APP_SUPABASE_URL'), Deno.env.get('SERVICE_ROLE_KEY'));
    if (event.type === 'checkout.session.completed') {
      const session = event.data.object;
      // Add additional logging
      console.log('Processing webhook for session ID:', session.id);
      console.log('Payment intent:', session.payment_intent);
      console.log('Client reference ID:', session.client_reference_id);
      console.log('Customer email:', session.customer_email);
      console.log('Metadata:', session.metadata);

      if (!session.client_reference_id) {
        console.error('No client_reference_id found in session - rejecting payment');
        throw new Error('No client_reference_id found in session');
      }

      // Reject guest payments - require authenticated users only
      const isGuest = session.client_reference_id.startsWith('guest-') || session.metadata?.isGuest === 'true';
      if (isGuest) {
        console.error('Guest payment detected - rejecting payment for session:', session.id);
        throw new Error('Guest payments are not allowed. User must be authenticated.');
      }

      const userId = session.client_reference_id;

      // Verify the user exists in our system
      const { data: userProfile, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id')
        .eq('id', userId)
        .single();

      if (profileError || !userProfile) {
        console.error('User profile not found for ID:', userId);
        throw new Error('User profile not found - payment rejected');
      }

      // Check for existing purchase using session ID
      const { data: existingPurchase } = await supabaseAdmin.from('purchases').select().eq('metadata->checkout_session_id', session.id).single();
      if (existingPurchase) {
        console.log('Purchase already processed for session:', session.id);
        return new Response(JSON.stringify({
          received: true,
          status: 'duplicate'
        }), {
          status: 200,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        });
      }
      // Check if user is already a card holder
      const { data: profileData } = await supabaseAdmin.from('profiles').select('is_card_holder').eq('id', userId).single();
      if (profileData?.is_card_holder) {
        console.log('User is already a card holder:', userId);
        return new Response(JSON.stringify({
          received: true,
          status: 'already_member'
        }), {
          status: 200,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        });
      }
      // Record the purchase
      const { error: purchaseError } = await supabaseAdmin.from('purchases').insert({
        user_id: userId,
        method: 'stripe',
        amount: session.amount_total / 100,
        status: 'completed',
        transaction_id: session.payment_intent,
        metadata: {
          checkout_session_id: session.id,
          payment_status: session.payment_status,
          customer_email: session.customer_email
        }
      }).select().single();
      if (purchaseError) {
        throw new Error(`Failed to record purchase: ${purchaseError.message}`);
      }
      // Update user profile
      const { error: updateError } = await supabaseAdmin.from('profiles').update({
        is_card_holder: true,
        membership_start_date: new Date().toISOString(),
        membership_end_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
      }).eq('id', userId);
      if (updateError) {
        console.error(`Failed to update profile: ${updateError.message}`);
      // Continue processing even if profile update fails
      }
      console.log('Successfully processed purchase for session:', session.id);
    }
    return new Response(JSON.stringify({
      received: true
    }), {
      status: 200,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
    console.error('❌ Error:', errorMessage);
    return new Response(JSON.stringify({
      error: errorMessage
    }), {
      status: 400,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
