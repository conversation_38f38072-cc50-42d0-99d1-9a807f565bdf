import { NextResponse } from "next/server"
import Stripe from "stripe"
import { createAdminClient } from "@/lib/supabase/server"

// Initialize Stripe with proper error handling
const stripeSecretKey = process.env.STRIPE_SECRET_KEY
if (!stripeSecretKey) {
  throw new Error("STRIPE_SECRET_KEY environment variable is not set")
}

const stripe = new Stripe(stripeSecretKey, {
  apiVersion: "2024-12-18.acacia",
})

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET
if (!endpointSecret) {
  throw new Error("STRIPE_WEBHOOK_SECRET environment variable is not set")
}

export async function POST(request: Request) {
  console.log('🔔 Stripe webhook received')

  const payload = await request.text()
  const signature = request.headers.get("stripe-signature") || ""

  let event

  try {
    event = stripe.webhooks.constructEvent(payload, signature, endpointSecret)
    console.log('✅ Webhook signature verified, event type:', event.type)
  } catch (err) {
    if (err instanceof Error) {
      console.error(`❌ Webhook signature verification failed: ${err.message}`)
    } else {
      console.error("❌ Webhook signature verification failed with an unknown error")
    }
    return NextResponse.json({ error: "Webhook signature verification failed" }, { status: 400 })
  }

  // Handle the checkout.session.completed event
  if (event.type === "checkout.session.completed") {
    const session = event.data.object as Stripe.Checkout.Session
    console.log('💳 Processing checkout.session.completed for session:', session.id)

    // Extract user ID from client_reference_id (preferred) or metadata
    const userId = session.client_reference_id || session.metadata?.userId
    const cardType = session.metadata?.cardType || 'Premium'

    try {
      // Handle both guest and authenticated user payments
      const isGuest = !userId || userId === "guest" || userId.startsWith('guest-') || session.metadata?.isGuest === 'true';

      if (isGuest) {
        console.log("🎫 Processing guest payment for session:", session.id)

        // Use admin client for reliable database access
        const supabase = createAdminClient()

        // For guest payments, create a purchase record without profile requirement
        const { data: purchaseData, error: purchaseError } = await supabase
          .from('purchases')
          .insert({
            user_id: null, // No user ID for guests
            guest_email: session.customer_email,
            amount: session.amount_total ? session.amount_total / 100 : 0,
            currency: 'usd',
            method: 'stripe',
            stripe_session_id: session.id,
            stripe_payment_intent_id: session.payment_intent,
            card_type: cardType,
            status: 'completed'
          })
          .select()
          .single()

        if (purchaseError) {
          console.error('❌ Failed to create guest purchase record:', purchaseError)
          throw new Error('Failed to create guest purchase record')
        }

        console.log('✅ Guest purchase recorded successfully:', purchaseData.id)
        return NextResponse.json({ received: true, status: 'guest_purchase_completed' })
      }

      // For authenticated users, process normally
      console.log("👤 Processing authenticated user payment for session:", session.id)

      // Use admin client for reliable database access
      const supabase = createAdminClient()

      // Try to get user profile, but don't require it for payment processing
      const { getUserProfileDirect } = await import('@/lib/database-direct');
      let userProfile = await getUserProfileDirect(userId);

      // If no profile exists, create a minimal one for the user
      if (!userProfile) {
        console.log('📝 Creating minimal profile for user:', userId)
        const { ensureUserProfileDirect } = await import('@/lib/database-direct');
        userProfile = await ensureUserProfileDirect(userId, {
          email: session.customer_email,
          is_card_holder: true
        });
      }

      console.log('✅ User profile verified for payment:', userId);

      // Check for existing purchase using session ID to prevent duplicates
      const { data: existingPurchase } = await supabase
        .from('purchases')
        .select('id')
        .eq('metadata->checkout_session_id', session.id)
        .single()

      if (existingPurchase) {
        console.log('⚠️ Purchase already processed for session:', session.id)
        return NextResponse.json({ received: true, status: 'duplicate' })
      }

      // Record the purchase
      const { error: purchaseError } = await supabase
        .from('purchases')
        .insert({
          user_id: userId,
          method: 'stripe',
          amount: (session.amount_total || 0) / 100, // Convert from cents
          status: 'completed',
          transaction_id: session.payment_intent as string,
          metadata: {
            checkout_session_id: session.id,
            payment_status: session.payment_status,
            customer_email: session.customer_email,
            card_type: cardType
          }
        })

      if (purchaseError) {
        console.error('❌ Failed to record purchase:', purchaseError)
        throw new Error(`Failed to record purchase: ${purchaseError.message}`)
      }

      // Update user profile to mark as card holder using direct database access
      try {
        const { updateUserProfileDirect } = await import('@/lib/database-direct');
        const updateResult = await updateUserProfileDirect(userId, {
          is_card_holder: true,
          card_tier: cardType,
          membership_start_date: new Date().toISOString(),
          membership_end_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
        });

        if (updateResult) {
          console.log('✅ Profile updated successfully for card holder:', userId);
        } else {
          console.error('⚠️ Failed to update profile via direct access');
          // Fallback to Supabase admin client
          const { error: updateError } = await supabase
            .from('profiles')
            .update({
              is_card_holder: true,
              card_tier: cardType,
              membership_start_date: new Date().toISOString(),
              membership_end_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
            })
            .eq('id', userId);

          if (updateError) {
            console.error('⚠️ Fallback profile update also failed:', updateError.message);
          }
        }
      } catch (directUpdateError) {
        console.error('⚠️ Direct profile update failed:', directUpdateError);
        // Continue processing even if profile update fails
      }

      console.log('✅ Successfully processed purchase for session:', session.id)
      return NextResponse.json({ received: true, status: 'processed' })

    } catch (error) {
      console.error("❌ Error processing webhook:", error)
      return NextResponse.json({
        error: "Error processing webhook",
        details: error instanceof Error ? error.message : String(error)
      }, { status: 500 })
    }
  }

  console.log('ℹ️ Webhook event type not handled:', event.type)
  return NextResponse.json({ received: true })
}
